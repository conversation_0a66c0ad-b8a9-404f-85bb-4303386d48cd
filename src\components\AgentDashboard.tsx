import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  LayoutDashboard,
  Phone,
  PhoneCall,
  Clock,
  Calendar,
  CheckCircle,
  AlertCircle,
  Database,
  Zap,
  Bell,
  TrendingUp,
  TrendingDown,
  Trophy,
  Medal,
  Award,
  Lightbulb,
  Target,
  Quote,
} from "lucide-react";
import agentAvatar from "@/assets/agent-avatar.jpg";

const AgentDashboard = () => {
  const [activeFilter, setActiveFilter] = useState("Day");

  const stats = [
    {
      title: "Total Dials",
      value: "1200",
      change: "+10%",
      trend: "up",
      icon: Phone,
    },
    {
      title: "Connected Calls",
      value: "450",
      change: "+5%",
      trend: "up",
      icon: Phone<PERSON>all,
    },
    {
      title: "Talk Time",
      value: "25h 30m",
      change: "+8%",
      trend: "up",
      icon: Clock,
    },
    {
      title: "Scheduled Meetings",
      value: "30",
      change: "+12%",
      trend: "up",
      icon: Calendar,
    },
    {
      title: "Successful Meetings",
      value: "20",
      change: "+15%",
      trend: "up",
      icon: CheckCircle,
    },
    {
      title: "Backlogs",
      value: "5",
      change: "-5%",
      trend: "down",
      icon: AlertCircle,
    },
  ];

  const milestones = [
    {
      icon: Trophy,
      title: "Most Dials in a Day: 150",
      date: "June 10",
      color: "text-warning",
    },
    {
      icon: Medal,
      title: "Highest Talk Time: 5h 12m",
      date: "May 28",
      color: "text-success",
    },
    {
      icon: Award,
      title: "100 Successful Meetings",
      date: "Achieved",
      color: "text-primary",
    },
  ];

  const insights = [
    {
      icon: Lightbulb,
      label: "Tip:",
      text: "Try calling leads between 9-10 AM for better connect rates.",
      color: "text-primary",
    },
    {
      icon: Target,
      label: "Goal:",
      text: "Increase connected calls by 5% this week.",
      color: "text-success",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card border-b border-border px-6 lg:px-10 py-4 shadow-sm">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <LayoutDashboard className="h-8 w-8 text-primary" />
              <h1 className="text-xl font-semibold text-foreground">
                Agent Dashboard
              </h1>
            </div>
            <div className="flex items-center gap-2 sm:gap-4">
              <div className="flex rounded-md border border-border bg-card p-0.5">
                {["Day", "Week", "Month"].map((filter) => (
                  <Button
                    key={filter}
                    variant={activeFilter === filter ? "default" : "ghost"}
                    size="sm"
                    className="text-xs sm:text-sm h-8"
                    onClick={() => setActiveFilter(filter)}
                  >
                    {filter}
                  </Button>
                ))}
              </div>
              <Button variant="outline" size="sm" className="text-xs sm:text-sm">
                <Database className="h-4 w-4 mr-1 hidden sm:inline" />
                Data Logs
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2 sm:gap-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="text-sm font-medium">
                  <Zap className="h-5 w-5 mr-1 hidden sm:inline" />
                  Quick Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem>
                  <Calendar className="h-4 w-4 mr-2" />
                  Request Leave
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Phone className="h-4 w-4 mr-2" />
                  Log a Call
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Create Task
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Bell className="h-5 w-5" />
            </Button>
            <div className="h-10 w-10 rounded-full border-2 border-primary overflow-hidden">
              <img
                src={agentAvatar}
                alt="Agent Avatar"
                className="h-full w-full object-cover"
              />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 p-4 sm:p-6 lg:p-8 xl:p-10">
        <div className="grid grid-cols-12 gap-4 sm:gap-6">
          {/* Agent Profile Card */}
          <Card className="col-span-12 lg:col-span-4 p-4 sm:p-6">
            <div className="flex flex-col items-center @[300px]:flex-row @[300px]:items-start gap-4">
              <div className="h-20 w-20 @[300px]:h-24 @[300px]:w-24 rounded-full border-2 border-gray-200 overflow-hidden">
                <img
                  src={agentAvatar}
                  alt="Sophia Clark"
                  className="h-full w-full object-cover"
                />
              </div>
              <div className="flex flex-col justify-center text-center @[300px]:text-left">
                <h2 className="text-lg sm:text-xl font-semibold text-foreground">
                  Sophia Clark
                </h2>
                <p className="text-xs sm:text-sm text-muted-foreground">
                  Sales Representative
                </p>
                <Badge className="mt-2 bg-blue-100 text-primary hover:bg-blue-100 self-center @[300px]:self-start">
                  <Trophy className="h-3 w-3 mr-1" />
                  Productivity Score: 92
                </Badge>
              </div>
            </div>
          </Card>

          {/* Stats Grid */}
          <div className="col-span-12 lg:col-span-8 grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4">
            {stats.map((stat, index) => (
              <Card key={index} className="p-3 sm:p-4">
                <div className="flex items-center justify-between text-muted-foreground mb-1">
                  <p className="text-xs sm:text-sm font-medium">{stat.title}</p>
                  <stat.icon className="h-4 w-4 sm:h-5 sm:w-5" />
                </div>
                <p className="text-xl sm:text-2xl font-bold text-foreground mb-1">
                  {stat.value}
                </p>
                <div
                  className={`flex items-center gap-1 ${
                    stat.trend === "up" ? "text-success" : "text-destructive"
                  }`}
                >
                  {stat.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4" />
                  ) : (
                    <TrendingDown className="h-3 w-3 sm:h-4 sm:w-4" />
                  )}
                  <p className="text-xs sm:text-sm font-medium">{stat.change}</p>
                </div>
              </Card>
            ))}
          </div>

          {/* Performance Chart */}
          <Card className="col-span-12 lg:col-span-7 p-4 sm:p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-base sm:text-lg font-semibold text-foreground">
                  Performance Trend
                </h3>
                <p className="text-xs sm:text-sm text-muted-foreground">
                  Last 3 Months
                </p>
              </div>
              <div className="flex items-center gap-1 text-success">
                <p className="text-xl sm:text-2xl font-bold">+12%</p>
              </div>
            </div>
            <div className="h-[180px] sm:h-[220px] w-full">
              <svg
                width="100%"
                height="100%"
                viewBox="0 0 472 150"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                preserveAspectRatio="none"
              >
                <defs>
                  <linearGradient
                    id="chartGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop offset="0%" stopColor="hsl(var(--primary))" stopOpacity="0.3" />
                    <stop offset="100%" stopColor="hsl(var(--primary))" stopOpacity="0" />
                  </linearGradient>
                </defs>
                <path
                  d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25V149H0V109Z"
                  fill="url(#chartGradient)"
                />
                <path
                  d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25"
                  stroke="hsl(var(--primary))"
                  strokeWidth="3"
                  strokeLinecap="round"
                  fill="none"
                />
              </svg>
            </div>
            <div className="flex justify-around pt-2 border-t border-border">
              {["Wk 1", "Wk 2", "Wk 3", "Wk 4", "Wk 5", "Wk 6", "Wk 7"].map(
                (week) => (
                  <p
                    key={week}
                    className="text-[10px] sm:text-xs font-medium text-muted-foreground"
                  >
                    {week}
                  </p>
                )
              )}
            </div>
          </Card>

          {/* Right Column */}
          <div className="col-span-12 lg:col-span-5 flex flex-col gap-4 sm:gap-6">
            {/* Personal Bests */}
            <Card className="p-4 sm:p-5">
              <h3 className="text-base sm:text-lg font-semibold text-foreground mb-3">
                Personal Bests & Milestones
              </h3>
              <div className="space-y-3">
                {milestones.map((milestone, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <milestone.icon className={`h-5 w-5 ${milestone.color}`} />
                      <p className="text-xs sm:text-sm text-foreground">
                        {milestone.title}
                      </p>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {milestone.date}
                    </span>
                  </div>
                ))}
              </div>
            </Card>

            {/* Actionable Insights */}
            <Card className="p-4 sm:p-5">
              <h3 className="text-base sm:text-lg font-semibold text-foreground mb-3">
                Actionable Insights
              </h3>
              <div className="space-y-3">
                {insights.map((insight, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <insight.icon className={`h-4 w-4 mt-0.5 ${insight.color}`} />
                    <p className="text-xs sm:text-sm text-muted-foreground">
                      <span className="font-semibold text-foreground">
                        {insight.label}
                      </span>{" "}
                      {insight.text}
                    </p>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Motivational Widget */}
          <Card className="col-span-12 p-4 sm:p-6">
            <h3 className="text-base sm:text-lg font-semibold text-foreground mb-3">
              Motivational Widget
            </h3>
            <div className="flex flex-col sm:flex-row items-center gap-4">
              <div className="flex-shrink-0">
                <Quote className="h-12 w-12 text-warning" />
              </div>
              <div className="flex-1 text-center sm:text-left">
                <p className="text-sm sm:text-base italic text-muted-foreground">
                  "The secret of getting ahead is getting started."
                </p>
                <p className="text-xs sm:text-sm text-right font-medium text-foreground mt-1">
                  - Mark Twain
                </p>
              </div>
              <div className="w-full sm:w-1/3 mt-3 sm:mt-0">
                <p className="text-xs sm:text-sm font-medium text-muted-foreground mb-1">
                  Weekly Goal Progress (Meetings Booked)
                </p>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-primary h-2.5 rounded-full transition-all duration-300"
                    style={{ width: "75%" }}
                  />
                </div>
                <p className="text-xs text-right text-muted-foreground mt-1">
                  75% (15/20)
                </p>
              </div>
            </div>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default AgentDashboard;