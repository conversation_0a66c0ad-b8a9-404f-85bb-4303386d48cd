// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rwxagwzlregwiierhkgw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ3eGFnd3pscmVnd2lpZXJoa2d3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwMzE0MTQsImV4cCI6MjA2OTYwNzQxNH0.tObRCXGu-mnnBXf6A_o6GVGm4EqFcchXI7gb0BGrFVE";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});